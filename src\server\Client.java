package server;
import database.DatabaseManager;
import database.PlayerDAO;
import lombok.Getter;
import map.ItemMap;
import player.Player;
import network.SessionManager;
import network.inetwork.ISession;
import network.MySession;
import services.Service;
import map.Service.ChangeMapService;
import services.func.TransactionService;
import Deputyhead.Service.NgocRongNamecService;
import utils.Functions;
import utils.Logger;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;

public class Client implements Runnable {

    private static Client instance;

    private final Map<Long, Player> players_id = new HashMap<>();
    private final Map<Integer, Player> players_userId = new HashMap<>();
    private final Map<String, Player> players_name = new HashMap<>();
    @Getter
    private final List<Player> players = new ArrayList<>();

    private Client() {
        Executors.newSingleThreadExecutor().submit(this, "Update Client");
    }

    public static Client gI() {
        if (instance == null) {
            instance = new Client();
        }
        return instance;
    }

    public void put(Player player) {
        if (!players_id.containsKey(player.id)) {
            this.players_id.put(player.id, player);
        }
        if (!players_name.containsValue(player)) {
            this.players_name.put(player.name, player);
        }
        if (!players_userId.containsValue(player)) {
            this.players_userId.put(player.getSession().userId, player);
        }
        if (!players.contains(player)) {
            this.players.add(player);
        }

    }

    private void remove(MySession session) {
        if (session.player != null) {
            this.remove(session.player);
            session.player.dispose();
        }
        if (session.joinedGame) {
            session.joinedGame = false;
            try {
                DatabaseManager.executeUpdate("update account set last_time_logout = ? where id = ?", new Timestamp(System.currentTimeMillis()), session.userId);
            } catch (Exception e) {
                Logger.logException(Client.class, e);
            }
        }
        ServerManager.gI().disconnect(session);
    }

    private void remove(Player player) {
        this.players_id.remove(player.id);
        this.players_name.remove(player.name);
        this.players_userId.remove(player.getSession().userId);
        this.players.remove(player);
        if (!player.beforeDispose) {
            player.beforeDispose = true;
            player.mapIdBeforeLogout = player.zone.map.mapId;
            if (player.idNRNM != -1) {
                ItemMap itemMap = new ItemMap(player.zone, player.idNRNM, 1, player.location.x, player.location.y, -1);
                Service.gI().dropItemMap(player.zone, itemMap);
                NgocRongNamecService.gI().pNrNamec[player.idNRNM - 353] = "";
                NgocRongNamecService.gI().idpNrNamec[player.idNRNM - 353] = -1;
                player.idNRNM = -1;
            }
            ChangeMapService.gI().exitMap(player);
            TransactionService.gI().cancelTrade(player);
            if (player.clan != null) {
                player.clan.removeMemberOnline(null, player);
            }
            // Handle SummonDragon disconnect safely
            handleSummonDragonDisconnect(player);

            // Handle SummonDragonNamek disconnect safely
            handleSummonDragonNamekDisconnect(player);
            if (player.shenronEvent != null) {
                player.shenronEvent.isPlayerDisconnect = true;
            }
            if (player.mobMe != null) {
                player.mobMe.mobMeDie();
            }
            if (player.pet != null) {
                if (player.pet.mobMe != null) {
                    player.pet.mobMe.mobMeDie();
                }
                ChangeMapService.gI().exitMap(player.pet);
            }
        }
        PlayerDAO.updatePlayer(player);
    }

    public void kickSession(MySession session) {
        if (session != null) {
            this.remove(session);
            session.disconnect();
        }
    }

    public Player getPlayer(long playerId) {
        return this.players_id.get(playerId);
    }

    public Player getPlayerByUser(int userId) {
        return this.players_userId.get(userId);
    }

    public Player getPlayer(String name) {
        return this.players_name.get(name);
    }

    public void close() {
        Logger.log(Logger.YELLOW, "BEGIN KICK OUT SESSION " + players.size() + "\n");
        while (!players.isEmpty()) {
            Player pl = players.remove(0);
            if (pl != null && pl.getSession() != null) {
                this.kickSession(pl.getSession());
            }
        }
        Logger.success("SUCCESSFUL\n");
    }

    private void update() {
        for (int i = SessionManager.gI().getSessions().size() - 1; i >= 0; i--) {
            ISession s = SessionManager.gI().getSessions().get(i);
            MySession session = (MySession) s;
            if (session == null) {
                SessionManager.gI().getSessions().remove(i);
                continue;
            }
            if (session.timeWait > 0) {
                session.timeWait--;
                if (session.timeWait == 0) {
                    kickSession(session);
                }
            }
        }
    }
    public Player getPlayerByID(int playerId) {
        for (int i = 0; i < players.size(); i++) {
            Player player = players.get(i);
            if (player != null && player.id == playerId) {
                return player;
            }
        }
        return null;
    }
    /**
     * Safely handle SummonDragon disconnect using reflection to prevent NoClassDefFoundError
     */
    private void handleSummonDragonDisconnect(Player player) {
        try {
            Class<?> summonDragonClass = Class.forName("services.shenron.SummonDragon");
            Object instance = summonDragonClass.getMethod("gI").invoke(null);

            Object playerSummonShenron = summonDragonClass.getField("playerSummonShenron").get(instance);
            if (playerSummonShenron != null) {
                long playerId = (Long) playerSummonShenron.getClass().getField("id").get(playerSummonShenron);
                if (playerId == player.id) {
                    summonDragonClass.getField("isPlayerDisconnect").set(instance, true);
                }
            }
        } catch (ClassNotFoundException e) {
            Logger.error("SummonDragon class not found - skipping disconnect handling for player: " + player.name + "\n");
        } catch (Exception e) {
            Logger.logException(Client.class, e, "Lỗi khi xử lý SummonDragon disconnect cho player: " + player.name);
        }
    }

    /**
     * Safely handle SummonDragonNamek disconnect using reflection to prevent NoClassDefFoundError
     */
    private void handleSummonDragonNamekDisconnect(Player player) {
        try {
            Class<?> summonDragonNamekClass = Class.forName("services.shenron.SummonDragonNamek");
            Object instance = summonDragonNamekClass.getMethod("gI").invoke(null);

            Object playerSummonShenron = summonDragonNamekClass.getField("playerSummonShenron").get(instance);
            if (playerSummonShenron != null) {
                long playerId = (Long) playerSummonShenron.getClass().getField("id").get(playerSummonShenron);
                if (playerId == player.id) {
                    summonDragonNamekClass.getField("isPlayerDisconnect").set(instance, true);
                }
            }
        } catch (ClassNotFoundException e) {
            Logger.error("SummonDragonNamek class not found - skipping disconnect handling for player: " + player.name + "\n");
        } catch (Exception e) {
            Logger.logException(Client.class, e, "Lỗi khi xử lý SummonDragonNamek disconnect cho player: " + player.name);
        }
    }

    @Override
    public void run() {
        while (ServerManager.isRunning) {
            long st = System.currentTimeMillis();
            try {
                update();
            } catch (Exception e) {
                e.printStackTrace();
            }
            Functions.sleep(Math.max(1000 - (System.currentTimeMillis() - st), 10));
        }
    }
}
