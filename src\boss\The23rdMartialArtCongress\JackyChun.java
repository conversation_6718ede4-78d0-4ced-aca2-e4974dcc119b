package boss.The23rdMartialArtCongress;


import boss.BossID;
import boss.BossesData;
import static consts.BossType.PHOBAN;
import player.Player;

public class <PERSON>y<PERSON><PERSON> extends The23rdMartialArtCongress {

    public Jacky<PERSON>hun(Player player) throws Exception {
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, BossID.JACKY_CHUN, BossesData.JACKY_CHUN);
        this.playerAtt = player;
    }
}
