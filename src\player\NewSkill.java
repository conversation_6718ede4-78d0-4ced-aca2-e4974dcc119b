package player;

/*
 * @Author: <PERSON><PERSON>RongBlackGoku
 * @Description: <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> Teamobi 2024
 * @Group Zalo: https://zalo.me/g/qabzvn331
 */


import item.Item;
import mob.Mob;
import skill.Skill;
import services.SkillService;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class NewSkill {

    public static final int TIME_GONG = 2000;
    public static final int TIME_END_24_25 = 3000;
    public static final int TIME_END_26 = 11000;

    private Player player;

    public NewSkill(Player player) {
        this.player = player;
        this.playersTaget = new ArrayList<>();
        this.mobsTaget = new ArrayList<>();
    }

    public Skill skillSelect;

    public byte dir;

    public short _xPlayer;

    public short _yPlayer;

    public short _xObjTaget;

    public short _yObjTaget;

    public List<Player> playersTaget;

    public List<Mob> mobsTaget;

    public boolean isStartSkillSpecial;

    public byte stepSkillSpecial;
    
    public byte typePaint;

    public byte typeItem;
    
    public long lastTimeSkillSpecial;
    public byte getTypePaint() {
        Item item = player.inventory.itemsBody.get(10);
        if (item != null && item.isNotNullItem()) {
            int itemId = item.template.id;
            if (itemId == 1044 || itemId == 1211 || itemId == 1212) {
                return 2;
            }
            if (itemId == 1278 || itemId == 1279 || itemId == 1280) {
                return 3;
            }
        }
        return 1;
    }

    public byte getTypeItem() {
        Item item = player.inventory.itemsBody.get(10);
        if (item != null && item.isNotNullItem()) {
            int itemId = item.template.id;
            if (itemId == 1279 && player.gender == 1) {
                return 2;
            }
           return 1;
        }
        return 0;
    }
    private void update() {
        if (this.isStartSkillSpecial == true) {
            SkillService.gI().updateSkillSpecial(player);
        }
    }

    public void setSkillSpecial(byte dir, short _xPlayer, short _yPlayer, short _xObjTaget, short _yObjTaget) {
        try {
            // Validate player and skill state
            if (player == null || player.playerSkill == null || player.playerSkill.skillSelect == null) {
                return;
            }

            if (player.itemTime != null && player.itemTime.isUseNCD) {
                typeItem = 2;
            } else {
                typeItem = 0;
            }

            this.skillSelect = this.player.playerSkill.skillSelect;

            // Null check for skillSelect
            if (this.skillSelect == null || this.skillSelect.template == null) {
                return;
            }

            if (this.player.isPl() && skillSelect.currLevel < 1000) {
                skillSelect.currLevel++;
                SkillService.gI().sendCurrLevelSpecial(player, skillSelect);
            }

            this.dir = dir;
            this._xPlayer = _xPlayer;
            this._yPlayer = _yPlayer;

            int length = _xObjTaget - _xPlayer;
            int dx = dir * (skillSelect.point * 100 + 100);

            if (skillSelect.template.id != Skill.MA_PHONG_BA) {
                if (Math.abs(dx) < Math.abs(length) || Math.abs(length) < 100) {
                    this._xObjTaget = (short) dx;
                } else {
                    this._xObjTaget = (short) length;
                }
            } else {
                this._xObjTaget = 75;
            }

            this._xObjTaget = (short) Math.abs(this._xObjTaget);
            this._yObjTaget = (short) Math.abs(_yObjTaget);
            this.isStartSkillSpecial = true;
            this.stepSkillSpecial = 0;
            this.lastTimeSkillSpecial = System.currentTimeMillis();
            this.start(250);

        } catch (Exception e) {
            // If skill setup fails, ensure we don't leave in inconsistent state
            this.isStartSkillSpecial = false;
            this.close();
        }
    }

    public void sonPhiPhai() {
        if (player.isAdmin()) {
            typePaint = -1;
        }
    }

    public synchronized void closeSkillSpecial() {
        try {
            this.isStartSkillSpecial = false;
            this.stepSkillSpecial = 0;

            // Safe clear of collections
            if (this.playersTaget != null) {
                this.playersTaget.clear();
            }
            if (this.mobsTaget != null) {
                this.mobsTaget.clear();
            }

            this.close();
        } catch (Exception e) {
            // Ensure cleanup even if error occurs
            this.isStartSkillSpecial = false;
            this.stepSkillSpecial = 0;
        }
    }

    private Timer timer;
    private TimerTask timerTask;
    private boolean isActive = false;

    private synchronized void close() {
        try {
            this.isActive = false;

            // Cancel timer task first
            if (this.timerTask != null) {
                try {
                    this.timerTask.cancel();
                } catch (Exception ex) {
                    // Ignore
                }
                this.timerTask = null;
            }

            // Then cancel and purge timer
            if (this.timer != null) {
                try {
                    this.timer.cancel();
                    this.timer.purge(); // Remove cancelled tasks from queue
                } catch (Exception ex) {
                    // Ignore
                }
                this.timer = null;
            }

        } catch (Exception e) {
            // Ensure cleanup even if error occurs
            this.isActive = false;
            this.timer = null;
            this.timerTask = null;
        }
    }

    public synchronized void start(int leep) {
        if (this.isActive == false) {
            try {
                // Clean up any existing timer first
                if (this.timer != null) {
                    try {
                        this.timer.cancel();
                        this.timer.purge(); // Remove cancelled tasks
                    } catch (Exception ex) {
                        // Ignore cleanup errors
                    }
                }
                if (this.timerTask != null) {
                    try {
                        this.timerTask.cancel();
                    } catch (Exception ex) {
                        // Ignore cleanup errors
                    }
                }

                this.isActive = true;
                this.timer = new Timer("NewSkill-Timer-" + (player != null ? player.name : "unknown"), true); // Daemon thread
                this.timerTask = new TimerTask() {
                    @Override
                    public void run() {
                        try {
                            if (player == null || player.newSkill == null || !isActive) {
                                close();
                                return;
                            }
                            NewSkill.this.update();
                        } catch (Exception e) {
                            // Log error and close to prevent further issues
                            try {
                                close();
                            } catch (Exception ex) {
                                // Ignore cleanup errors during error handling
                            }
                        }
                    }
                };
                this.timer.schedule(timerTask, leep, leep);
            } catch (Exception e) {
                // If timer creation fails, ensure cleanup
                this.isActive = false;
                this.timer = null;
                this.timerTask = null;
            }
        }
    }

    public void dispose() {
        try {
            // Close any active timers first
            this.close();
            // Clear references
            this.player = null;
            this.skillSelect = null;
            if (this.playersTaget != null) {
                this.playersTaget.clear();
            }
            if (this.mobsTaget != null) {
                this.mobsTaget.clear();
            }
        } catch (Exception e) {
            // Ensure cleanup even if error occurs
            this.player = null;
            this.skillSelect = null;
        }
    }

}
