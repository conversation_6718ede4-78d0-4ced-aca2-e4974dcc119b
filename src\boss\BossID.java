package boss;
public class BossID {

    public static final int TRUNG_UY_TRANG = -4;
    public static final int TRUNG_UY_THEP = -5;
    public static final int TRUNG_UY_XANH_LO = -6;
    public static final int NINJA_AO_TIM = -7;
    public static final int ROBOT_VE_SI = -8;

    public static final int NINJA_AO_TIM1 = -9;
    public static final int NINJA_AO_TIM2 = -10;
    public static final int NINJA_AO_TIM3 = -11;
    public static final int NINJA_AO_TIM4 = -12;
    public static final int NINJA_AO_TIM5 = -13;
    public static final int NINJA_AO_TIM6 = -14;

    public static final int CADICH = -15;
    public static final int NADIC = -16;
    public static final int SAIBAMEN = -17;

    public static final int KUKU = -20;
    public static final int MAP_DAU_DINH = -21;
    public static final int RAMBO = -22;

    public static final int SO_4 = -23;
    public static final int SO_3 = -24;
    public static final int SO_2 = -25;
    public static final int SO_1 = -26;
    public static final int TIEU_DOI_TRUONG = -27;

    public static final int TDST = -223;

    public static final int FIDE = -28;

    public static final int COOLER = -29;

    public static final int ANDROID_19 = -30;
    public static final int DR_KORE = -31;

    public static final int ANDROID_13 = -32;
    public static final int ANDROID_14 = -33;
    public static final int ANDROID_15 = -34;

    public static final int PIC = -35;
    public static final int POC = -36;
    public static final int KING_KONG = -37;

    public static final int XEN_BO_HUNG = -100;
    public static final int SIEU_BO_HUNG = -101;
    public static final int XEN_CON_1 = -102;
    public static final int XEN_CON_2 = -103;
    public static final int XEN_CON_3 = -104;
    public static final int XEN_CON_4 = -105;
    public static final int XEN_CON_5 = -106;
    public static final int XEN_CON_6 = -107;
    public static final int XEN_CON_7 = -108;

    public static final int HIT = -204;
    public static final int CHILL_1 = -205;
    public static final int CHILL_2 = -206;
    public static final int HATCHIYACK = -207;
    public static final int DR_LYCHEE = -208;

    public static final int BROLY = -1;
    public static final int SUPER_BROLY = -2;

    public static final int SOI_HEC_QUYN1 = -77;
    public static final int O_DO1 = -78;
    public static final int Virut = -79;

    public static final int DRABURA = -233;
    public static final int BUI_BUI = -234;
    public static final int YA_CON = -235;
    public static final int MABU_12H = -236;
    public static final int DRABURA_2 = -237;
    public static final int BUI_BUI_2 = -238;
    public static final int GOKU = -341;
    public static final int CADIC = -342;
    public static final int DRABURA_3 = -343;

    public static final int BLACK_GOKU = -203;

    public static final int TAU_PAY_PAY_DONG_NAM_KARIN = -308;
    public static final int TAUPAYPAY = -309;
    public static final int SO_4_NM = -311;
    public static final int SO_3_NM = -312;
    public static final int SO_2_NM = -313;
    public static final int SO_1_NM = -314;
    public static final int TIEU_DOI_TRUONG_NM = -315;

    public static final int BUJIN = -316;
    public static final int KOGU = -317;
    public static final int ZANGYA = -318;
    public static final int BIDO = -319;
    public static final int BOJACK = -320;
    public static final int SUPER_BOJACK = -321;

    public static final int TAP_SU_0 = -322;
    public static final int TAP_SU_1 = -323;
    public static final int TAP_SU_2 = -324;
    public static final int TAP_SU_3 = -325;
    public static final int TAP_SU_4 = -326;
    public static final int TAN_BINH_5 = -327;
    public static final int TAN_BINH_0 = -328;
    public static final int TAN_BINH_1 = -329;
    public static final int TAN_BINH_2 = -330;
    public static final int TAN_BINH_3 = -331;
    public static final int TAN_BINH_4 = -332;
    public static final int CHIEN_BINH_5 = -333;
    public static final int CHIEN_BINH_0 = -334;
    public static final int CHIEN_BINH_1 = -335;
    public static final int CHIEN_BINH_2 = -336;
    public static final int CHIEN_BINH_3 = -337;
    public static final int CHIEN_BINH_4 = -338;
    public static final int DOI_TRUONG_5 = -339;

    public static final int POCOLO = -340;

    public static final int SOI_HEC_QUYN = -77;
    public static final int O_DO = -78;
    public static final int XINBATO = -79;
    public static final int CHA_PA = -80;
    public static final int PON_PUT = -81;
    public static final int CHAN_XU = -82;
    public static final int TAU_PAY_PAY = -83;
    public static final int YAMCHA = -84;
    public static final int JACKY_CHUN = -85;
    public static final int THIEN_XIN_HANG = -86;
    public static final int LIU_LIU = -87;
    public static final int THIEN_XIN_HANG_CLONE = -88;
    public static final int THIEN_XIN_HANG_CLONE1 = -89;
    public static final int THIEN_XIN_HANG_CLONE2 = -90;
    public static final int THIEN_XIN_HANG_CLONE3 = -91;
    public static final int PO_CO_LO = -92;
    public static final int DRACULA = -93;
    public static final int NGUOI_VO_HINH = -94;
    public static final int BONG_BANG = -95;
    public static final int VUA_QUY_SA_TANG = -96;
    public static final int THO_DAU_BAC = -97;

    public static final int KHIDOT = -344;
    public static final int NGUYETTHAN = -345;
    public static final int NHATTHAN = -346;

    public static final int GOLDEN_FRIEZA = -502;
    public static final int DEATH_BEAM_1 = -609;
    public static final int DEATH_BEAM_2 = -610;
    public static final int DEATH_BEAM_3 = -611;
    public static final int DEATH_BEAM_4 = -612;
    public static final int DEATH_BEAM_5 = -613;

    public static final int HAKAI = -613;

    public static final int MABU = -214;
    public static final int SUPERBU = -348;

    public static final int MATROI = -349;
    public static final int DOI = -350;
    public static final int BIMA = -351;

    public static final int ONG_GIA_NOEL = -353;

    public static final int SON_TINH = -354;
    public static final int THUY_TINH = -355;

    public static final int LAN_CON = -371;

    public static final int KARIN = -357;
    public static final int YAJIRO = -358;
    public static final int MRPOPO = -359;
    public static final int THUONG_DE = -360;
    public static final int KHI_BUBBLES = -361;
    public static final int THAN_VU_TRU = -362;
    public static final int TO_SU_KAIO = -363;
    public static final int WHIS = -364;
    public static final int BABY = -3971;
    public static final int CHILLED =-9171881;
    public static final int AN_TROM = -365;
    public static final int BROLY_BASE = -3972;
    public static final int TAU_PAIPAI = -385;
}
