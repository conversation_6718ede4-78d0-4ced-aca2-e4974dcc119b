package services.shenron;

import player.Player;

/**
 * Stub class to prevent NoClassDefFoundError when SummonDragon is not available
 * This is a temporary workaround for compilation issues
 */
public class SummonDragonStub {
    
    private static SummonDragonStub instance;
    
    public boolean isPlayerDisconnect = false;
    public Player playerSummonShenron = null;
    
    private SummonDragonStub() {
        // Private constructor for singleton
    }
    
    public static SummonDragonStub gI() {
        if (instance == null) {
            instance = new SummonDragonStub();
        }
        return instance;
    }
    
    // Stub methods to prevent compilation errors
    public void openMenuSummonShenron(Player pl, byte dragonBallStar) {
        // Stub implementation - does nothing
    }
    
    public void summonShenron(Player pl) {
        // Stub implementation - does nothing
    }
    
    public void reOpenShenronWishes(Player pl) {
        // Stub implementation - does nothing
    }
    
    public void shenronLeave(Player pl, byte type) {
        // Stub implementation - does nothing
    }
}
