package consts;

public class ConstNpc {

    public static final String CALICK_KE_CHUYEN = "20 năm trước bọn Android sát thủ đã đánh bại nhóm bảo vệ trái đất của Sôngoku và Cađíc, Pôcôlô ...\n"
            + "Riêng Sôngoku vì bệnh tim nên đã chết trước đó nên không thể tham gia trận đánh...\n"
            + "Từ đó đến nay bọn chúng tàn phá Trái Đất không hề thương tiếc\n"
            + "Cháu và mẹ may mắn sống sót nhờ lẩn trốn tại tần hầm của công ty Capsule...\n"
            + "Cháu tuy cũng là siêu xayda nhưng cũng không thể làm gì được bọn Android sát thủ...\n"
            + "Chỉ có Sôngoku mới có thể đánh bại bọn chúng\n"
            + "mẹ cháu đã chế tạo thành công cỗ máy thời gian\n"
            + "và cháu quay về quá khứ để cứu Sôngoku...\n"
            + "Bệnh của Gôku ở quá khứ là nan y, nhưng với trình độ y học tương lai chỉ cần uống thuốc là khỏi...\n"
            + "Hãy đi theo cháu đến tương lai giúp nhóm của Gôku đánh bạn bọn Android sát thủ\n"
            + "Khi nào chú cần sự giúp đỡ của cháu hãy đến đây nhé";

    public static final String HUONG_DAN_DOANH_TRAI = "1) Trại độc nhãn là nơi các ngươi không nên vào vì những tướng tá rất mạnh. Hahaha\b"
            + "2) Trong trại độc nhãn, mỗi vị tướng đều giữ ngọc rồng từ 4 sao đến 6 sao, tùy lúc\n"
            + "3) Nếu ngươi thích chết thì cứ việc vào. Nhưng ta chỉ cho vào mỗi ngày một lần thôi, để ngươi khỏi phải chết nhiều, hahaha.\b"
            + "4) Các vị tướng trong trại rất mạnh nhé, các ngươi không đơn giản có thể đánh bại họ bằng cách bình thường như đánh quái được đâu\n"
            + "5) Muốn vào, ngươi phải đi cùng một người đồng đội cùng bang (phải đứng gần ngươi). Nhưng ta khuyên là nên đi 3-4 người cùng.\b"
            + "6) Mỗi lần vào, ngươi chỉ có 30 phút để đánh. Sau 30 phút mà ngươi vẫn không thắng, ta sẽ cho máy bay chở ngươi về nhà.";

    public static final String HUONG_DAN_BLACK_BALL_WAR = "Mỗi ngày từ 20h đến 21h các hành tinh có Ngọc Rồng Sao Đen sẽ xảy ra 1 cuộc đại chiến\n"
            + "Người nào tìm thấy và giữ được Ngọc Rồng Sao Đen sẽ mang phần thưởng về cho bang của mình trong vòng 1 ngày\n"
            + "Lưu ý mỗi bang có thể chiếm hữu nhiều viên khác nhau nhưng nếu cùng loại cũng chỉ nhận được 1 lần phần thưởng đó. Có 2 cách để thắng:\n"
            + "1) Giữ ngọc sao đen trên người hơn 5 phút liên tục\n"
            + "2) Sau 30 phút tham gia tàu sẽ đón về và đang giữ ngọc sao đen trên người\n"
            + "Các phần thưởng như sau\b1 sao đen: +21% sức đánh\b2 sao đen: +35% HP\b"
            + "3 sao đen: Biến 35% tấn công thành HP\b4 sao đen: Phản 35% sát thương\b"
            + "5 sao đen: 35% Sức đánh chí mạng\b6 sao đen: KI+40%\b7 sao đen: 14% Né đòn";
    public static final String HUONG_DAN_MAP_MA_BU = "Tại khu vực này, ta đã dùng ma pháp phong ấn\b"
            + "dù các ngươi có mạnh đến đâu\bcũng sẽ trở thành yếu đuối như nhau\n"
            + "Chỉ có ta với con nhóc Ôsin mới giải được ma pháp này\b"
            + "Khi đó sức mạnh của ngươi sẽ được\bgiải phóng theo điểm tích lũy (TL)\n"
            + "Khi đủ điểm tích lũy bằng cách\bhạ lẫn nhau hoặc hạ boss\b"
            + "ta sẽ đưa ngươi xuống tầng tiếp theo";
    public static final String HUONG_DAN_DOI_SKH_VIP = "Nguyên liệu cần để làm SKH VIP là :\n"
            + "1 Lọ Sơn Tăng Dame, 1 món Thiên Sứ và 2 món SKH thường\n"
            + "Lưu ý SKH VIP sẽ tạo ra đưa vào món Thiên Sứ\n"
            + "Ví dụ nguyên liệu gồm : Quần Xayda Thiên Tứ + 2 món SKH thường ngẫu nhiên\n"
            + "Bạn sẽ nhận lại được Quần Xayda với chỉ số SKH VIP";

    public static final String NPC_DHVT23 = "Đại hội quy tụ nhiều cao thủ như là Jacky Chun, Thiên Xin Hăng, Tàu Bảy Bảy... Phần thường là 1 rương gỗ chưa nhiều vật phẩm giá trị.\bKhi hạ được 1 đối thủ, phần thưởng sẽ nâng lên 1 cấp.\bRương càng cao cấp, vật phẩm trong đó càng giá trị hơn.\n"
            + "Mỗi ngày bạn chỉ được nhận 1 phần thưởng.\bBạn hãy cố gắng hết sức mình\bđể nhận phần thưởng xứng đáng nhất nhé";

    public static final String HUONG_DAN_KHI_GAS_HUY_DIET = "Chúng ta gặp rắc rối rồi\bThượng Đế nói với tôi rằng có 1 loại khí\bgọi là Destron Gas, thứ này không thuộc về nơi đây\n"
            + "Nó tích tụ trên Trái Đất\bvà nó sẽ hủy diệt mọi mô tế bào sống\bCó tất cả 4 địa điểm mà Thượng Đế bảo tôi nói với cậu\bCậu có thể đến kiểm tra...\n"
            + "Đầu tiên là Thành phố Santa tọa lạc ở phía tây nam của thủ đô ở Viễn Đông.\n"
            + "Thứ hai là gần Kim Tự Tháp ở vùng Sa Mạc viễn tây của thủ đô Phía Bắc.\n"
            + "Thứ ba Vùng Đất Băng Giá ở Phương Bắc xa xôi\n"
            + "Thứ tư là Hành tinh Bóng Tối đang che phủ một phần địa cầu\bCậu đã hiểu rõ chưa?";

    public static final String HUONG_DAN_BILL = "Đại hội võ thuật liên vũ trụ\blà nơi quy tụ các cao thủ từ khắp các Vũ Trụ\bĐược tổ chức hàng ngày tại các thời điểm như sau:\n"
            + "Thứ 2: 6h, Thứ 3: 13h, Thứ 4: 15h\bThứ 5: 17h, Thứ 6: 18h, Thứ 7: 12h, Chủ Nhật 10h\bVới các phần thưởng vô cùng hấp dẫn như sau\n"
            + "Top 10: 1 phiếu giảm giá, 1 capsule Vàng\bTop 1: tặng thêm 1 Rađa cấp 13\bPhần thưởng được trao tại vũ trụ của chiến binh\bĐến gặp Whis tại vũ trụ của ngươi để nhận thưởng";

    public static final String HUONG_DAN_NRNM = "Để gọi được Rồng Thần Namếc, anh cần phải làm các việc sau\b"
            + "1) Đang có bang hội\b2) Tập họp đủ 7 viên ngọc rồng Namếc tại đây\b3) Em sẽ gọi rồng và anh nào giữ ngọc 1 sao sẽ được chọn điều ước\b"
            + "4) Thời gian gọi Rồng Thần là 8h-22h\n"
            + "(Lưu ý) Điều ước sẽ có tác dụng với tất cả thành viên trong bang có mặt tại đây\b"
            + "Sau khi điều ước được thực hiện, tất cả ngọc sẽ biến thành đá trong 1 ngày\b"
            + "Những ai vừa nhận điều ước, hoặc bang hội nhận điều ước\b"
            + "phải chờ 7 ngày sau mới có thể nhận điều ước khác";
    public static final String TAP_TU_DONG = "Tập luyện vẫn tiếp tục và sức mạnh vẫn tăng khi đã Offline\n"
            + "Hiệu quả tập luyện như sau:\bThần Mèo: 20 sức mạnh mỗi phút\bYajirô: 40 sức mạnh mỗi phút\bMr.PôPô: 80 sức mạnh mỗi phút\bThượng đế: 160 sức mạnh mỗi phút\n"
            + "Khỉ Bubbles: 320 sức mạnh mỗi phút\bThần Vũ Trụ: 640 sức mạnh mỗi phút\bTổ sư Kaio: 1280 sức mạnh mỗi phút\n"
            + "Có thể tặng ngọc để thắng mà không cần thách đấu\n"
            + "Nếu đăng ký tập thường xuyên mỗi khi Offline không cần phải đến đây vẫn tập luyện được";
    public static final String THONG_TIN_DAI_HOI_VO_THUAT = "Lịch thi đấu trong ngày\bGiải Nhi đồng: 8,14,18h\bGiải Siêu cấp 1: 9,13,19h\bGiải Siêu Cấp 2: 10,15,20h\bGiải Siêu cấp 3: 11,16,21h\bGiải Ngoại hạng: 12,17,22,23h\n"
            + "Giải thưởng khi thắng mỗi vòng\bGiải Nhi đồng: 2 ngọc\bGiải Siêu cấp 1: 4 ngọc\bGiải Siêu cấp 2: 6 ngọc\bGiải Siêu cấp 3: 8 ngọc\bGiải Ngoại hạng: 10.000 vàng\bVô địch: 5 viên đá nâng cấp\n"
            + "Lệ phí đăng ký các giải đấu\bGiải Nhi đồng: 2 ngọc\bGiải Siêu cấp 1: 4 ngọc\bGiải Siêu cấp 2: 6 ngọc\bGiải Siêu cấp 3: 8 ngọc\bGiải Ngoại hạng: 10.000 vàng\n"
            + "Vui lòng đến đúng giờ để đăng ký thi đấu";
    public static final String THONG_TIN_SIEU_HANG = "Giải đấu thể hiện đẳng cấp thực sự\bCác trận đấu diễn ra liên tục bất kể ngày đêm\bBạn hãy tham gia thi đấu để nâng hạng\bvà nhận giải thưởng khủng nhé\n"
            + "Cơ cấu giải thưởng như sau\b(chốt và trao giải ngẫu nhiên từ 20h-23h mỗi ngày)\bTop 1 thưởng 100 ngọc\bTop 2-10 thưởng 20 ngọc\bTop 11-100 thưởng 5 ngọc\bTop 101-1000 thưởng 1 ngọc\n"
            + "Mỗi ngày các bạn được tặng 1 vé tham dự miễn phí\b(tích lũy tối đa 3 vé) khi thua sẽ mất đi 1 vé\bKhi hết vé bạn phải trả 1 ngọc để đấu tiếp\b(trừ ngọc khi trận đấu kết thúc)\n"
            + "Bạn không thể thi đấu với đấu thủ\bcó hạng nhỏ hơn mình\bChúc bạn may mắn, chào đoàn kết và quyết thắng";
    public static final byte ONG_GOHAN = 0;
    public static final byte ONG_PARAGUS = 1;
    public static final byte ONG_MOORI = 2;
    public static final byte RUONG_DO = 3;
    public static final byte DAU_THAN = 4;
    public static final byte CON_MEO = 5;
    public static final byte KHU_VUC = 6;
    public static final byte BUNMA = 7;
    public static final byte DENDE = 8;
    public static final byte APPULE = 9;
    public static final byte DR_DRIEF = 10;
    public static final byte CARGO = 11;
    public static final byte CUI = 12;
    public static final byte QUY_LAO_KAME = 13;
    public static final byte TRUONG_LAO_GURU = 14;
    public static final byte VUA_VEGETA = 15;
    public static final byte URON = 16;
    public static final byte BO_MONG = 17;
    public static final byte THAN_MEO_KARIN = 18;
    public static final byte THUONG_DE = 19;
    public static final byte THAN_VU_TRU = 20;
    public static final byte BA_HAT_MIT = 21;
    public static final byte TRONG_TAI = 22;
    public static final byte GHI_DANH = 23;
    public static final byte RONG_THIENG = 24;
    public static final byte LINH_CANH = 25;
    public static final byte DOC_NHAN = 26;
    public static final byte RONG_THIENG_NAMEC = 27;
    public static final byte CUA_HANG_KY_GUI = 28;
    public static final byte RONG_OMEGA = 29;
    public static final byte RONG_2S = 30;
    public static final byte RONG_3S = 31;
    public static final byte RONG_4S = 32;
    public static final byte RONG_5S = 33;
    public static final byte RONG_6S = 34;
    public static final byte RONG_7S = 35;
    public static final byte RONG_1S = 36;
    public static final byte BUNMA_TL = 37;
    public static final byte CALICK = 38;
    public static final byte SANTA = 39;
    public static final byte MABU_MAP = 40;
    public static final byte TRUNG_THU = 41;
    public static final byte QUOC_VUONG = 42;
    public static final byte TO_SU_KAIO = 43;
    public static final byte OSIN = 44;
    public static final byte KIBIT = 45;
    public static final byte BABIDAY = 46;
    public static final byte GIUMA_DAU_BO = 47;
    public static final byte NGO_KHONG = 48;
    public static final byte DUONG_TANG = 49;
    public static final byte QUA_TRUNG = 50;
    public static final byte DUA_HAU = 51;
    public static final byte HUNG_VUONG = 52;
    public static final byte TAPION = 53;
    public static final byte LY_TIEU_NUONG = 54;
    public static final byte BILL = 55;
    public static final byte WHIS = 56;
    public static final byte CHAMPA = 57;
    public static final byte VADOS = 58;
    public static final byte TRONG_TAI_2 = 59;
    public static final byte GOKU_SSJ = 60;
    public static final byte GOKU_SSJ_2 = 61;
    public static final byte POTAGE = 62;
    public static final byte JACO = 63;
    public static final byte DAI_THIEN_SU = 64;
    public static final byte YARIROBE = 65;
    public static final byte NOI_BANH = 66;
    public static final byte MR_POPO = 67;
    public static final byte PANCHY = 68;
    public static final byte THO_DAI_CA = 69;
    public static final byte BARDOCK = 70;
    public static final byte TORIBOT = 71;
    public static final byte CAY_NEU = 72;
    public static final byte EVENT = 75;
    public static final byte NPC_MAP_164 = 80;
    public static final int GO_UPSTAIRS_MENU = 10000;

    public static final int IGNORE_MENU = 72002;
    public static final int BASE_MENU = 31072002;
    public static final int ORTHER_MENU = 50394;

    public static final int ORTHER_MENU1 = 23505;
    public static final int ORTHER_MENU2 = 53906;

    public static final int MENU_OPEN_DBKB = 500;
    public static final int MENU_OPENED_DBKB = 501;
    public static final int MENU_ACCEPT_GO_TO_BDKB = 502;

    public static final int SANTA_PGG = 601;

    public static final int MENU_DI_CHUYEN = 500;

    public static final int MENU_PHA_LE_HOA_TRANG_BI = 500;
    public static final int MENU_CHUYEN_HOA_TRANG_BI = 501;
    public static final int MENU_OPTION_SHOP_BUA = 502;
    public static final int MENU_START_COMBINE = 503;
    public static final int MENU_PHAN_RA_DO_THAN_LINH = 504;
    public static final int MENU_NANG_CAP_DO_TS = 505;
    public static final int MENU_NANG_DOI_SKH_VIP = 506;
    public static final int MENU_CHE_TAO_CHAN_MENH = 507;
    public static final int MENU_NANG_CAP_CHAN_MENH = 508;
    public static final int MENU_NANG_CAP_DAC_CAU = 509;
    public static final int MENU_SACH_TUYET_KY = 510;
    public static final int MENU_DAP_DO = 508;

    public static final int MENU_JOIN_DOANH_TRAI = 502;

    public static final int MAKE_MATCH_PVP = 502;
    public static final int MAKE_FRIEND = 503;
    public static final int REVENGE = 504;
    public static final int TUTORIAL_SUMMON_DRAGON = 505;
    public static final int SUMMON_SHENRON = 506;
    public static final int INTRINSIC = 507;
    public static final int CONFIRM_OPEN_INTRINSIC = 508;
    public static final int CONFIRM_OPEN_INTRINSIC_VIP = 509;
    public static final int CONFIRM_LEAVE_CLAN = 510;
    public static final int CONFIRM_NHUONG_PC = 511;
    public static final int MENU_ADMIN = 512;
    public static final int BAN_PLAYER = 513;
    public static final int BUFF_PET = 514;
    public static final int CONFIRM_REMOVE_ALL_ITEM_LUCKY_ROUND = 515;
    public static final int MENU_FIND_PLAYER = 516;
    public static final int CONFIRM_DISSOLUTION_CLAN = 517;
    public static final int CONFIRM_TELE_NAMEC = 522;
    public static final int UP_TOP_ITEM = 527;
    public static final int MA_BAO_VE = 528;
    public static final int OTT = 529;
    public static final int OTT_ACCEPT = 530;
    public static final int RUONG_GO = 531;
    public static final int DAT_CUOC_HAT_MIT = 532;

    public static final int BUY_BACK = 533;
    public static final int MENU_OPTION_USE_ITEM726 = 726;
    public static final int MENU_SIEU_THAN_THUY = 2006;
    public static final int MENU_XUONG_TANG_DUOI = 2007;
    public static final int SUMMON_SHENRON_SUPER = 928;
    public static final int SUMMON_SHENRON_NOEL = 929;
    public static final int SUMMON_SHENRON_HALLOWEEN = 930;

    public static final int SHENRON_CONFIRM = 501;
    public static final int SHENRON_1_1 = 502;
    public static final int SHENRON_1_2 = 503;
    public static final int SHENRON_1_3 = 506;
    public static final int SHENRON_2 = 504;
    public static final int SHENRON_3 = 505;
    public static final int SHOW_SHENRON_NAMEK_CONFIRM = 31720020;
    public static final int SHENRON_NAMEK_CONFIRM = 31720021;
    public static final int SUMMON_SHENRON_EVENT = 31720022;
    public static final int SHOW_SHENRON_EVENT_CONFIRM = 31720023;
    public static final int SHENRON_EVENT_CONFIRM = 31720024;
    public static final int SUMMON_SHENRON_EVENT_NOEL = 31720225;
    public static final int SHOW_SHENRON_EVENT_CONFIRM_NOEL = 3170026;
    public static final int SHENRON_EVENT_CONFIRM_NOEL = 3172039;

    public static final int SUB_MENU = 31720025;
    public static final int TAP_TU_DONG_CONFIRM = 31720026;
    public static final int MENU_CLAN_UP = 4;
    public static final int MENU_CLAN_TASK = 5;
    public static final int MENU_CLAN_TASK_REMOVE = 6;

    public static final int DANGKYDHVT_CONFIRM = 0;

    public static final int MAGIC_TREE_NON_UPGRADE_LEFT_PEA = 501;
    public static final int MAGIC_TREE_NON_UPGRADE_FULL_PEA = 502;
    public static final int MAGIC_TREE_CONFIRM_UPGRADE = 503;
    public static final int MAGIC_TREE_UPGRADE = 504;
    public static final int MAGIC_TREE_CONFIRM_UNUPGRADE = 505;

    public static final int CAN_NOT_OPEN_EGG = 500;
    public static final int CAN_OPEN_EGG = 501;
    public static final int CONFIRM_OPEN_EGG = 502;
    public static final int CONFIRM_DESTROY_EGG = 503;

    public static final int CAN_NOT_OPEN_BILL = 500;
    public static final int CAN_OPEN_BILL = 501;
    public static final int CONFIRM_OPEN_BILL = 502;
    public static final int CONFIRM_DESTROY_BILL = 503;

    public static final int QUA_TAN_THU = 500;
    public static final int MENU_PHAN_THUONG = 501;
    public static final int NAP_THE = 502;

    public static final int OPEN_POWER_MYSEFT = 500;
    public static final int OPEN_POWER_PET = 501;

    public static final int MENU_CHOOSE_LUCKY_ROUND = 500;

    public static final int MENU_FIND_KUKU = 501;
    public static final int MENU_FIND_MAP_DAU_DINH = 502;
    public static final int MENU_FIND_RAMBO = 503;

    public static final int MENU_NOT_OPEN_BDW = 500;
    public static final int MENU_OPEN_BDW = 501;
    public static final int MENU_REWARD_BDW = 502;
    public static final int MENU_NOT_OPEN_MMB = 500;
    public static final int MENU_OPEN_MMB = 501;
    public static final int MENU_REWARD_MMB = 502;

    public static final int MENU_PHU_HP = 500;
    public static final int MENU_OPTION_PHU_HP = 501;
    public static final int MENU_OPTION_GO_HOME = 502;

    public static final int MENU_OPTION_LEVEL_SIDE_TASK = 500;
    public static final int MENU_OPTION_PAY_SIDE_TASK = 501;
    public static final int event3 = 3333;
 public static final int event3_1 = 3334;

    public static final int SHOP_BUNMA_QK_0 = 500;
    public static final int SHOP_DENDE_0 = 501;
    public static final int SHOP_APPULE_0 = 502;
    public static final int SHOP_SANTA_0 = 503;
    public static final int SHOP_SANTA_1 = 504;
    public static final int SHOP_URON_0 = 505;
    public static final int SHOP_BA_HAT_MIT_0 = 506;
    public static final int SHOP_BA_HAT_MIT_1 = 507;
    public static final int SHOP_BA_HAT_MIT_2 = 508;
    public static final int SIDE_BOX_LUCKY_ROUND = 509;
    public static final int SHOP_BUNMA_TL_0 = 510;
    public static final int SIDE_BOX_ITEM_REWARD = 511;
    public static final int HOC_TUYET_KY = 9990;
    public static final int SACH_TUYET_KY = 99980;
    public static final int DONG_THANH_SACH_CU = 19787;
    public static final int DOI_SACH_TUYET_KY = 28777;
    public static final int CHE_TAO_TRANG_BI_THIEN_SU = 188877;
    public static final int MOC_NAP = 192972;
}

