@echo off
echo Building NgocRongOnline...

REM Create build directories
if not exist "build\classes" mkdir "build\classes"
if not exist "dist" mkdir "dist"

REM Clean old build
del /Q "build\classes\*" 2>nul
del /Q "dist\*" 2>nul

echo Compiling Java sources...

REM Compile with UTF-8 encoding and proper classpath
javac -encoding UTF-8 -cp "lib/*;src" -d "build/classes" -sourcepath "src" src/server/ServerManager.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo Creating JAR file...

REM Create manifest
echo Main-Class: server.ServerManager > manifest.txt
echo Class-Path: lib/HikariCP-5.1.0.jar lib/apache-commons-lang.jar lib/bson-5.1.3.jar lib/gson-2.8.2.jar lib/java-json.jar lib/json-simple-1.1.jar lib/log4j-1.2.17.jar lib/lombok.jar lib/mysql-connector-java8-5.1.23.jar lib/slf4j-api-2.0.0-alpha1.jar lib/slf4j-simple-2.0.0-alpha1.jar >> manifest.txt

REM Create JAR
jar cfm "dist/NgocRongOnline.jar" manifest.txt -C "build/classes" . -C "." data

if %ERRORLEVEL% NEQ 0 (
    echo JAR creation failed!
    pause
    exit /b 1
)

REM Clean up
del manifest.txt

echo Build completed successfully!
echo JAR file created: dist/NgocRongOnline.jar
pause
