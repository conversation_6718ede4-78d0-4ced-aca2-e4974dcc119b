package consts;
public class ConstMob {

    public static final byte MOC_NHAN = 0;
    public static final byte KHUNG_LONG = 1;
    public static final byte LON_LOI = 2;
    public static final byte QUY_DAT = 3;
    public static final byte KHUNG_LONG_ME = 4;
    public static final byte LON_LOI_ME = 5;
    public static final byte QUY_DAT_ME = 6;
    public static final byte THAN_LAN_BAY = 7;
    public static final byte PHI_LONG = 8;
    public static final byte QUY_BAY = 9;
    public static final byte THAN_LAN_ME = 10;
    public static final byte PHI_LONG_ME = 11;
    public static final byte QUY_BAY_ME = 12;
    public static final byte OC_MUON_HON = 13;
    public static final byte OC_SEN = 14;
    public static final byte HEO_XAYDA_ME = 15;
    public static final byte HEO_RUNG = 16;
    public static final byte HEO_DA_XANH = 17;
    public static final byte HEO_XAYDA = 18;
    public static final byte HEO_RUNG_ME = 19;
    public static final byte HEO_XANH_ME = 20;
    public static final byte ALIEN = 21;
    public static final byte BULON = 22;
    public static final byte UKULELE = 23;
    public static final byte QUY_MAP = 24;
    public static final byte TAMBOURINE = 25;
    public static final byte DRUM = 26;
    public static final byte AKKUMAN = 27;
    public static final byte THAN_LAN_BAY_2 = 28;
    public static final byte PHI_LONG_2 = 29;
    public static final byte QUY_BAY_2 = 30;
    public static final byte KHONG_TAC = 31;
    public static final byte QUY_DAU_TO = 32;
    public static final byte QUY_DIA_NGUC = 33;
    public static final byte LINH_DOC_NHAN = 34;
    public static final byte LINH_DOC_NHAN2 = 35;
    public static final byte SOI_XAM = 36;
    public static final byte ROBOT_BAY = 37;
    public static final byte ROBOT_THEP = 38;
    public static final byte NAPPA = 39;
    public static final byte SOLDIER = 40;
    public static final byte APPULE = 41;
    public static final byte RASPBERRY = 42;
    public static final byte THAN_LAN_XANH = 43;
    public static final byte QUY_DAU_NHON = 44;
    public static final byte QUY_DAU_VANG = 45;
    public static final byte QUY_DA_TIM = 46;
    public static final byte QUY_GIA = 47;
    public static final byte CA_SAU = 48;
    public static final byte DOI_DA_XANH = 49;
    public static final byte QUY_CHIM = 50;
    public static final byte LINH_DAU_TROC = 51;
    public static final byte LINH_TAI_DAI = 52;
    public static final byte LINH_VU_TRU = 53;
    public static final byte KHI_LONG_DEN = 54;
    public static final byte KHI_GIAP_SAT = 55;
    public static final byte KHI_LONG_DO = 56;
    public static final byte KHI_LONG_VANG = 57;
    public static final byte XEN_CON_CAP_1 = 58;
    public static final byte XEN_CON_CAP_2 = 59;
    public static final byte XEN_CON_CAP_3 = 60;
    public static final byte XEN_CON_CAP_4 = 61;
    public static final byte XEN_CON_CAP_5 = 62;
    public static final byte XEN_CON_CAP_6 = 63;
    public static final byte XEN_CON_CAP_7 = 64;
    public static final byte XEN_CON_CAP_8 = 65;
    public static final byte TAI_TIM = 66;
    public static final byte ABO = 67;
    public static final byte KADO = 68;
    public static final byte DA_XANH = 69;
    public static final byte HIRUDEGARN = 70;
    public static final byte VUA_BACH_TUOC = 71;
    public static final byte ROBOT_BAO_VE = 72;
    public static final byte KAWAZU = 73;
    public static final byte KINKARN = 74;
    public static final byte ARBEE = 75;
    public static final byte CO_MAY_HUY_DIET = 76;
    public static final byte GAU_TUONG_CUOP = 77;
    public static final byte KHI_LONG_XANH = 78;
    public static final byte TABURINE_DO = 79;
    public static final byte CABIRA = 80;
    public static final byte TOBI = 81;
    public static final byte VOI_CHIN_NGA = 82;
    public static final byte GA_CHIN_CUA = 83;
    public static final byte NGUA_CHIN_LMAO = 84;
    public static final byte PIANO = 85;
    public static final byte ECH_MAT_DO = 86;
    public static final byte JINAI = 87;
    public static final byte QUY_DO = 88;
    public static final byte QUY_XANH = 89;

    public static final byte TOPPO = 94;
    public static final byte THO_CON = 95;
    public static final byte JANEMBA = 96;
    public static final byte MEZ = 97;
    public static final byte GOZ = 98;
    public static final byte DA_DO = 99;
    public static final byte DA_VANG = 100;
    public static final byte DA__XANH = 101;
    public static final byte THAY_MA = 102;
    public static final byte BU_NHIN_MA_QUAI = 103;
    public static final byte PHU_THUY = 104;
    public static final byte FROSTBITE = 105;
    public static final byte SNOWY_TANGERINE = 106;
    public static final byte DEINONYCHUS = 107;
    public static final byte SNAKE = 108;
    public static final byte BLIZZARD_BIRD = 109;

}
