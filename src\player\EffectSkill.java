package player;
import lombok.Setter;
import mob.Mob;
import services.EffectSkillService;
import services.ItemTimeService;
import utils.Util;

public class EffectSkill {

    @Setter
    private Player player;

    public boolean isStun;
    public long lastTimeStartStun;
    public int timeStun;

    public boolean isShielding;
    public long lastTimeShieldUp;
    public int timeShield;

    public boolean isMonkey;
    public byte levelMonkey;
    public long lastTimeUpMonkey;
    public int timeMonkey;

    public boolean isBinh;
    public int typeBinh;
    public long lastTimeUpBinh;
    public int timeBinh;
    public Player playerUseMafuba;

    public boolean isStone;
    public long lastTimeStone;
    public int timeStone;

    public boolean isMabuHold;

    public boolean isLamCham;
    public long lastTimeLamCham;
    public int timeLamCham;

    public boolean isTanHinh;
    public long lastTimeTanHinh;
    public int timeTanHinh;

    public boolean isPKCommeson;
    public long lastTimePKCommeson;
    public int timePKCommeson;

    public boolean isPKSTT;
    public long lastTimePKSTT;
    public int timePKSTT;

    public boolean isChibi;
    public long lastTimeChibi;
    public int timeChibi;

    public boolean isCharging;
    public int countCharging;

    public int tiLeHPHuytSao;
    public long lastTimeHuytSao;

    public boolean isThoiMien;
    public long lastTimeThoiMien;
    public int timeThoiMien;

    public boolean useTroi;
    public boolean anTroi;
    public long lastTimeTroi;
    public int timeTroi;
    public Player plTroi;
    public Player plAnTroi;
    public Mob mobAnTroi;

    public boolean isBlindDCTT;
    public long lastTimeBlindDCTT;
    public int timeBlindDCTT;

    public boolean isSocola;
    public long lastTimeSocola;
    public int timeSocola;
    public int countPem1hp;

    public boolean isHalloween;
    public long lastTimeHalloween;
    public int timeHalloween;
    public int idOutfitHalloween;

    public boolean isUseMafuba;
    public long lastTimeUseMafuba;
    public int timeUseMafuba;

    public boolean isUseSkillMonkey;
    public long lastTimeUseSkillMonkey;
    public int timeUseSkillMonkey;

    public boolean isIntrinsic;
    public long lastTimeUseSkill;
    public int skillID;
    public int cooldown;

    public boolean isDameBuff;
    public long lastTimeDameBuff;
    public int timeDameBuff;
    public int tileDameBuff;

    public boolean isBodyChangeTechnique;

    public EffectSkill(Player player) {
        this.player = player;
    }

    public void removeSkillEffectWhenDie() {
        if (isMonkey) {
            EffectSkillService.gI().monkeyDown(player);
        }
        if (isUseSkillMonkey) {
            EffectSkillService.gI().finishUseMonkey(player);
        }
        if (isBinh) {
            EffectSkillService.gI().BinhDown(player);
        }
        if (isShielding) {
            EffectSkillService.gI().removeShield(player);
            ItemTimeService.gI().removeItemTime(player, 3784);
        }
        if (useTroi) {
            EffectSkillService.gI().removeUseTroi(this.player);
        }
        if (isStun) {
            EffectSkillService.gI().removeStun(this.player);
        }
        if (isThoiMien) {
            EffectSkillService.gI().removeThoiMien(this.player);
        }
        if (isBlindDCTT) {
            EffectSkillService.gI().removeBlindDCTT(this.player);
        }
        if (isStone) {
            EffectSkillService.gI().removeStone(this.player);
        }
        if (isLamCham) {
            EffectSkillService.gI().removeLamCham(this.player);
        }
        if (isTanHinh) {
            EffectSkillService.gI().removeTanHinh(this.player);
        }
        if (isMabuHold) {
            EffectSkillService.gI().removeMabuHold(this.player);
        }
        if (isDameBuff) {
            EffectSkillService.gI().removeDameBuff(this.player);
        }
    }

    public void update() {
        if (isMonkey && (Util.canDoWithTime(lastTimeUpMonkey, timeMonkey))) {
            EffectSkillService.gI().monkeyDown(player);
        }
        if (isBinh && (Util.canDoWithTime(lastTimeUpBinh, timeBinh))) {
            EffectSkillService.gI().BinhDown(player);
        }
        if (isShielding && (Util.canDoWithTime(lastTimeShieldUp, timeShield))) {
            EffectSkillService.gI().removeShield(player);
        }
        if (useTroi && Util.canDoWithTime(lastTimeTroi, timeTroi)
                || plAnTroi != null && plAnTroi.isDie()
                || useTroi && isHaveEffectSkill()) {
            EffectSkillService.gI().removeUseTroi(this.player);
        }
        if (isStun && Util.canDoWithTime(lastTimeStartStun, timeStun)) {
            EffectSkillService.gI().removeStun(this.player);
        }
        if (isThoiMien && (Util.canDoWithTime(lastTimeThoiMien, timeThoiMien))) {
            EffectSkillService.gI().removeThoiMien(this.player);
        }
        if (isBlindDCTT && (Util.canDoWithTime(lastTimeBlindDCTT, timeBlindDCTT))) {
            EffectSkillService.gI().removeBlindDCTT(this.player);
        }
        if (isSocola && (Util.canDoWithTime(lastTimeSocola, timeSocola))) {
            EffectSkillService.gI().removeSocola(this.player);
        }
        if (tiLeHPHuytSao != 0 && Util.canDoWithTime(lastTimeHuytSao, 30000)) {
            EffectSkillService.gI().removeHuytSao(this.player);
        }
        if (isStone && Util.canDoWithTime(lastTimeStone, timeStone)) {
            EffectSkillService.gI().removeStone(this.player);
        }
        if (isLamCham && Util.canDoWithTime(lastTimeLamCham, timeLamCham)) {
            EffectSkillService.gI().removeLamCham(this.player);
        }
        if (isTanHinh && Util.canDoWithTime(lastTimeTanHinh, timeTanHinh)) {
            EffectSkillService.gI().removeTanHinh(this.player);
        }
        if (isPKCommeson && Util.canDoWithTime(lastTimePKCommeson, timePKCommeson)) {
            EffectSkillService.gI().removePKCommeson(this.player);
        }
        if (isPKSTT && Util.canDoWithTime(lastTimePKSTT, timePKSTT)) {
            EffectSkillService.gI().removePKSTT(this.player);
        }
        if (isChibi && Util.canDoWithTime(lastTimeChibi, timeChibi)) {
            EffectSkillService.gI().removeChibi(this.player);
        }
        if (isUseMafuba && Util.canDoWithTime(lastTimeUseMafuba, timeUseMafuba)) {
            EffectSkillService.gI().finishUseMafuba(player);
        }
        if (isUseSkillMonkey && Util.canDoWithTime(lastTimeUseSkillMonkey, timeUseSkillMonkey)) {
            EffectSkillService.gI().finishUseMonkey(player);
        }
        if (isIntrinsic && Util.canDoWithTime(lastTimeUseSkill, cooldown)) {
            EffectSkillService.gI().releaseCooldownSkillByIntrinsic(player);
        }
        if (isDameBuff && Util.canDoWithTime(lastTimeDameBuff, timeDameBuff)) {
            EffectSkillService.gI().removeDameBuff(this.player);
        }
    }

    public boolean isHaveEffectSkill() {
        return (isStun || isBlindDCTT || anTroi || isThoiMien || isStone || isMabuHold || isUseSkillMonkey) && !player.isDie();
    }

    public void dispose() {
        this.player = null;
        this.plAnTroi = null;
        this.plTroi = null;
        this.playerUseMafuba = null;
        this.mobAnTroi = null;
    }
}
