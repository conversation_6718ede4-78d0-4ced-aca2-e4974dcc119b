package network;

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>n Teamobi 2024
 * @Group Zalo: https://zalo.me/g/qabzvn331
 */


import network.KeyHandler;
import data.DataGame;
import network.inetwork.ISession;

public class MyKeyHandler extends KeyHandler {

    @Override
    public void sendKey(ISession session) {
        super.sendKey(session);
        DataGame.sendDataImageVersion((MySession) session);
        DataGame.sendVersionRes((MySession) session);
    }

}
